"use client";

import React, { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { useToggleImmigrationVisibility } from "@/hooks/use-query";
import { Loader2 } from "lucide-react";

interface VisibilityToggleProps {
  immigrationId: string;
  initialVisibility: boolean;
  disabled?: boolean;
}

export const VisibilityToggle: React.FC<VisibilityToggleProps> = ({
  immigrationId,
  initialVisibility,
  disabled = false,
}) => {
  const [isVisible, setIsVisible] = useState(initialVisibility);
  const { mutate: toggleVisibility, isPending } = useToggleImmigrationVisibility();

  const handleToggle = (newValue: boolean) => {
    // Optimistically update the UI
    setIsVisible(newValue);
    
    toggleVisibility(
      { id: immigrationId, website_visible: newValue },
      {
        onError: () => {
          // Revert the optimistic update on error
          setIsVisible(!newValue);
        },
      }
    );
  };

  return (
    <div className="flex items-center gap-2">
      {isPending && (
        <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
      )}
      <Switch
        checked={isVisible}
        onCheckedChange={handleToggle}
        disabled={disabled || isPending}
        aria-label={`Toggle website visibility for immigration service`}
      />
      <span className="text-sm text-muted-foreground">
        {isVisible ? "Visible" : "Hidden"}
      </span>
    </div>
  );
};
